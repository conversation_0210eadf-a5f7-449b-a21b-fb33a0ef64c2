import 'package:flutter/material.dart';

/// App color constants based on the custom color palette
class AppColors {
  AppColors._();

  // Custom Color Palette
  static const Color primary = Color(0xFF590219);
  static const Color secondary = Color(0xFF529AD9);
  static const Color accent = Color(0xFF39BF99);
  static const Color success = Color(0xFF39BF99);
  static const Color warning = Color(0xFFD99255);
  static const Color background = Color(0xFFF2F2F2);

  // Variations of primary color
  static const Color primaryLight = Color(0xFF7A2A3D);
  static const Color primaryDark = Color(0xFF3D0112);

  // Variations of secondary color
  static const Color secondaryLight = Color(0xFF73B3E7);
  static const Color secondaryDark = Color(0xFF3F7BAE);

  // Neutral colors
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color grey = Color(0xFF9E9E9E);
  static const Color lightGrey = Color(0xFFE0E0E0);
  static const Color darkGrey = Color(0xFF424242);

  // Error colors
  static const Color error = Color(0xFFD32F2F);
  static const Color errorLight = Color(0xFFEF5350);
  static const Color errorDark = Color(0xFFC62828);

  // Surface colors
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF5F5F5);
  static const Color onSurface = Color(0xFF1C1B1F);
  static const Color onSurfaceVariant = Color(0xFF49454F);

  // Text colors
  static const Color onPrimary = Color(0xFFFFFFFF);
  static const Color onSecondary = Color(0xFFFFFFFF);
  static const Color onBackground = Color(0xFF1C1B1F);
  static const Color onError = Color(0xFFFFFFFF);
}
