# Flockin Church Mobile App - Project Requirements Document

## 1. Project Overview

**Project Name:** Flockin Church Member Mobile App
**Platform:** Cross-platform mobile application (iOS & Android)
**Target Audience:** Church members and attendees
**Primary Purpose:** Digital companion for church members to access church content, services, and community features

## 2. Core Features & User Stories

### 2.1 Authentication & User Management

- **Member Registration**: Users can register with church invitation code
- **Login/Logout**: Secure authentication using church credentials
- **Profile Management**: Update personal information, preferences, and church membership details
- **Password Reset**: Self-service password recovery

### 2.2 Church Content Access

#### 2.2.1 Video Sermons & Messages

- Browse video sermons by category (Sunday Sermons, Bible Study, Testimonies, Special Events)
- Search videos by title, speaker, or scripture reference
- Video playback with controls (play, pause, seek, volume)
- Download videos for offline viewing
- Video series organization and tracking
- Featured/highlighted sermons
- View count and engagement metrics

#### 2.2.2 Hymns & Worship Music

- Browse hymn library by category (Traditional, Contemporary, Seasonal)
- Search hymns by title, author, or theme
- View lyrics with verse structure
- Play audio recordings when available
- Access chord charts and sheet music
- Favorite hymns for quick access
- Recently used hymns tracking

#### 2.2.3 Daily Devotionals

- Access daily devotional content
- Read devotionals by date or browse archives
- Scripture references with quick Bible lookup
- Reflection questions for personal study
- Prayer sections and spiritual guidance
- Share devotionals with others
- Schedule devotional notifications

### 2.3 Church Services & Events

#### 2.3.1 Service Information

- View upcoming service schedules
- Service details (time, location, type)
- Live service notifications
- Service history and attendance tracking

#### 2.3.2 Event Calendar

- Church event calendar integration
- Event details and registration
- Reminder notifications
- Add events to personal calendar

### 2.4 Church Check-in & Attendance

- Digital check-in for services
- Geofenced attendance tracking
- QR code check-in option
- Volunteer role assignment during check-in
- Family check-in for multiple members
- Check-in history and statistics

### 2.5 Church Directory & Community

- Access church member directory (with privacy settings)
- Contact information for church staff and leadership
- Prayer request submission and sharing
- Community announcements and news
- Church social media feed integration

### 2.6 Giving & Donations

- Secure online giving platform
- One-time and recurring donation setup
- Donation history and tax statements
- Multiple payment methods support
- Giving challenges and campaigns

### 2.7 Bible Study & Resources

- Integrated Bible reader
- Study guides and discussion materials
- Small group resources
- Scripture search and cross-references
- Personal notes and highlights

### 2.8 Notifications & Communication

- Push notifications for service reminders
- Church announcements and updates
- Prayer request notifications
- Event reminders
- Personalized content recommendations

## 3. User Interface Requirements

### 3.1 Design Principles

- **Clean & Modern**: Following current mobile design trends
- **Intuitive Navigation**: Easy-to-use bottom tab navigation
- **Church Branding**: Customizable colors and branding per church
- **Accessibility**: Support for screen readers and accessibility features
- **Responsive**: Optimized for various screen sizes

### 3.2 Key UI Screens

#### 3.2.1 Main Navigation Tabs

1. **Home**: Dashboard with featured content and quick actions
2. **Content**: Videos, hymns, and devotionals
3. **Services**: Schedule and check-in
4. **Community**: Directory and social features
5. **Profile**: User settings and preferences

#### 3.2.2 Essential Screens

- Splash/Loading screen with church branding
- Login and registration screens
- Content browsing and detail screens
- Video/audio player interfaces
- Check-in flow screens
- Profile and settings screens
- Search and filter interfaces

### 3.3 Theme & Styling

- Support for light and dark mode
- Church-specific color schemes
- Consistent typography and spacing
- Material Design or iOS Human Interface Guidelines compliance

## 4. Technical Requirements

### 4.1 Platform Specifications

- **Framework**: React Native or Flutter
- **Minimum iOS Version**: iOS 12.0+
- **Minimum Android Version**: Android 6.0 (API level 23)
- **Architecture**: MVVM or Clean Architecture pattern

### 4.2 Core Technologies

- **State Management**: Redux Toolkit or Context API
- **Navigation**: React Navigation or Flutter Navigator
- **HTTP Client**: Axios or Dio for API communication
- **Local Storage**: SQLite or Hive for offline data
- **Media Playback**: ExoPlayer (Android) / AVPlayer (iOS)
- **Push Notifications**: Firebase Cloud Messaging

### 4.3 Backend Integration

- **API Base URL**: Configurable per church instance
- **Authentication**: JWT token-based authentication
- **API Format**: RESTful JSON APIs
- **File Upload**: Multipart form data for media uploads
- **Real-time**: WebSocket or Server-Sent Events for live updates

## 5. Data Models & API Endpoints

### 5.1 Core Data Models

```typescript
// User/Member
interface ChurchMember {
    id: number;
    name: string;
    email: string;
    phone?: string;
    church_id: number;
    member_since: string;
    profile_image?: string;
}

// Church Information
interface Church {
    id: number;
    name: string;
    address: string;
    phone: string;
    website?: string;
    logo?: string;
    primary_color: string;
    secondary_color: string;
}

// Video Content
interface Video {
    id: number;
    title: string;
    description: string;
    speaker: string;
    recorded_date: string;
    duration: number;
    thumbnail_url: string;
    video_url: string;
    download_url?: string;
    category: ContentCategory;
    scripture_references: string[];
    view_count: number;
}

// Hymn Content
interface Hymn {
    id: number;
    title: string;
    author: string;
    composer?: string;
    lyrics: string;
    audio_url?: string;
    chord_chart_url?: string;
    sheet_music_url?: string;
    category: ContentCategory;
    themes: string[];
}

// Devotional Content
interface Devotional {
    id: number;
    title: string;
    content: string;
    author: string;
    devotional_date: string;
    key_verse: string;
    key_verse_reference: string;
    prayer: string;
    reflection_questions: string[];
    featured_image_url?: string;
}

// Service Information
interface Service {
    id: number;
    name: string;
    description: string;
    service_type: string;
    start_time: string;
    end_time: string;
    location: string;
    is_active: boolean;
}

// Attendance Record
interface AttendanceRecord {
    id: number;
    user_id: number;
    service_id: number;
    check_in_time: string;
    check_out_time?: string;
    location_lat?: number;
    location_lng?: number;
    check_in_method: 'manual' | 'qr' | 'geofence';
}
```

### 5.2 Required API Endpoints

```
Authentication:
POST /api/auth/login
POST /api/auth/register
POST /api/auth/logout
POST /api/auth/refresh
POST /api/auth/forgot-password
POST /api/auth/reset-password

Church Information:
GET /api/church/info
GET /api/church/settings
GET /api/church/directory

Content Management:
GET /api/content/videos
GET /api/content/videos/:id
GET /api/content/hymns
GET /api/content/hymns/:id
GET /api/content/devotionals
GET /api/content/devotionals/:id
GET /api/content/categories
GET /api/content/search

Services & Events:
GET /api/services
GET /api/services/:id
GET /api/events
GET /api/events/:id

Attendance:
POST /api/attendance/check-in
POST /api/attendance/check-out
GET /api/attendance/history
GET /api/services/:id/attendance

User Profile:
GET /api/user/profile
PUT /api/user/profile
GET /api/user/attendance-history
GET /api/user/favorites

Notifications:
POST /api/notifications/register-device
GET /api/notifications/preferences
PUT /api/notifications/preferences
```

## 6. Offline Capabilities

### 6.1 Offline Content

- Cache recently viewed videos for offline playback
- Store downloaded hymns and sheet music
- Sync devotionals for offline reading
- Cache church directory information
- Store service schedules locally

### 6.2 Sync Strategy

- Automatic sync when connected to WiFi
- Background sync for critical updates
- Manual refresh option for users
- Conflict resolution for data changes

## 7. Security & Privacy

### 7.1 Authentication Security

- Secure token storage using KeyChain (iOS) or Keystore (Android)
- Automatic token refresh mechanism
- Session timeout and re-authentication
- Biometric authentication support

### 7.2 Data Privacy

- Minimal data collection policy
- User consent for location tracking
- Secure transmission using HTTPS/TLS
- Local data encryption for sensitive information
- GDPR compliance considerations

## 8. Performance Requirements

### 8.1 App Performance

- App launch time: < 3 seconds
- Video playback: Smooth 720p/1080p streaming
- Image loading: Progressive loading with placeholders
- Search response: < 500ms for local queries
- API response handling: Graceful error management

### 8.2 Network Optimization

- Efficient caching strategy
- Image compression and lazy loading
- Adaptive video quality based on connection
- Retry mechanisms for failed requests
- Background data usage controls

## 9. Analytics & Monitoring

### 9.1 User Analytics

- Content consumption metrics
- User engagement tracking
- Feature usage statistics
- Crash reporting and error tracking
- Performance monitoring

### 9.2 Church Analytics

- Member attendance patterns
- Content popularity metrics
- App usage demographics
- Feature adoption rates

## 10. Future Enhancements

### 10.1 Phase 2 Features

- Live streaming integration
- Group messaging and chat
- Event RSVPs and management
- Small group management tools
- Digital bulletin and announcements

### 10.2 Advanced Features

- AI-powered content recommendations
- Voice search and commands
- Augmented reality church tours
- Integration with calendar apps
- Multi-language support

## 11. Development Phases

### Phase 1: Core MVP (Months 1-3)

- Authentication and user management
- Basic content browsing (videos, hymns, devotionals)
- Simple church information display
- Basic UI/UX implementation

### Phase 2: Enhanced Features (Months 4-6)

- Check-in and attendance tracking
- Advanced search and filtering
- Offline capabilities
- Push notifications
- Improved UI/UX

### Phase 3: Community Features (Months 7-9)

- Church directory
- Social features and sharing
- Enhanced content management
- Analytics integration
- Performance optimization

### Phase 4: Advanced Features (Months 10-12)

- Live streaming support
- Advanced notification system
- Comprehensive offline mode
- Additional integrations
- Testing and optimization

## 12. Success Metrics

### 12.1 User Engagement

- Daily active users (DAU)
- Monthly active users (MAU)
- Session duration and frequency
- Content consumption rates
- Feature adoption rates

### 12.2 Church Impact

- Digital attendance tracking accuracy
- Member engagement increase
- Content reach and impact
- Administrative efficiency gains
- Overall member satisfaction

---

_This document serves as the foundation for the Flockin Church Mobile App development. It should be reviewed and updated regularly as the project evolves and new requirements emerge._
