import 'package:flutter/material.dart';
import '../../core/theme/theme.dart';

/// Sample attendance item model
class AttendanceItem {
  final String status;
  final String description;
  final String time;
  final String? imageUrl;
  final bool isOnTime;

  AttendanceItem({
    required this.status,
    required this.description,
    required this.time,
    this.imageUrl,
    this.isOnTime = true,
  });
}

/// Attendance history section with list of attendance items
class AttendanceHistorySection extends StatelessWidget {
  const AttendanceHistorySection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Text(
          'Attendance History',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.black,
          ),
        ),

        const SizedBox(height: 16),

        // Segmented control for time period
        Container(
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: const [
              BoxShadow(
                color: Color(0x0A000000),
                blurRadius: 8,
                offset: Offset(0, 2),
              ),
            ],
          ),
          padding: const EdgeInsets.all(4),
          child: Row(
            children: [
              Expanded(child: _buildSegmentButton(context, 'This Week', false)),
              Expanded(child: _buildSegmentButton(context, 'This Month', true)),
            ],
          ),
        ),

        const SizedBox(height: 20),

        // Attendance item
        _buildAttendanceItem(
          context,
          'Arrived on time',
          'Wednesday, 10 February 2021 - 06:40',
          true,
        ),
      ],
    );
  }

  Widget _buildSegmentButton(
    BuildContext context,
    String text,
    bool isSelected,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      decoration: BoxDecoration(
        color: isSelected
            ? AppColors.secondary.withValues(alpha: 0.1)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        border: isSelected
            ? Border.all(color: AppColors.secondary, width: 1.5)
            : null,
      ),
      child: Center(
        child: Text(
          text,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            color: isSelected ? AppColors.secondary : AppColors.grey,
          ),
        ),
      ),
    );
  }

  Widget _buildAttendanceItem(
    BuildContext context,
    String status,
    String time,
    bool isOnTime,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(
            color: Color(0x08000000),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Status icon
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: _getStatusColor(isOnTime).withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              isOnTime ? Icons.check : Icons.access_time,
              color: _getStatusColor(isOnTime),
              size: 20,
            ),
          ),

          const SizedBox(width: 12),

          // Status text and time
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  status,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppColors.black,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  time,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.grey,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),

          // Check mark
          if (isOnTime)
            const Icon(Icons.check_circle, color: AppColors.success, size: 20),
        ],
      ),
    );
  }

  Color _getStatusColor(bool isOnTime) {
    return isOnTime ? AppColors.success : AppColors.warning;
  }
}
