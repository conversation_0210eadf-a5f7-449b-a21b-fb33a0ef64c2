import 'package:flutter/material.dart';
import '../../core/theme/theme.dart';
import '../widgets/custom_header.dart';
import '../widgets/profile/attendance_stats_section.dart';
import '../widgets/profile/profile_main_menu.dart';

/// Profile page with attendance stats and main menu
class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Full-width header with profile card
            const CustomHeader(
              pageTitle: 'Profile',
              userName: '<PERSON>',
              userBadges: ['Student', 'X Echo 1'],
            ),

            // Main content spacing
            const SizedBox(height: 24),

            // Attendance stats section
            const AttendanceStatsSection(),

            const SizedBox(height: 20),

            // Main menu section
            const ProfileMainMenuSection(),

            // Bottom spacing
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }
}
