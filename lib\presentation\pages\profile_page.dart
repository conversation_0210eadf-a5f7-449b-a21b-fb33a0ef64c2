import 'package:flutter/material.dart';
import '../../core/theme/theme.dart';
import '../widgets/custom_header.dart';

/// Profile page - placeholder for user profile settings
class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Full-width header with profile card
            const CustomHeader(
              pageTitle: 'Profile Page',
              userName: '<PERSON>',
              userBadges: ['Student', 'X Echo 1'],
            ),

            // Main content spacing
            const SizedBox(height: 24),

            // Main content with consistent padding
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 16),

                  // Profile content placeholder
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.black.withValues(alpha: 0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: const Column(
                      children: [
                        Icon(Icons.settings, size: 64, color: AppColors.grey),
                        SizedBox(height: 16),
                        Text(
                          'Profile Settings',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: AppColors.onSurface,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          'This page will show user profile information, settings, and account management options.',
                          textAlign: TextAlign.center,
                          style: TextStyle(fontSize: 16, color: AppColors.grey),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
