import 'package:flutter/material.dart';
import 'package:flockin_v2_app/core/theme/theme.dart';
import 'package:flockin_v2_app/presentation/widgets/common/custom_header.dart';
import 'announcement_detail_page.dart';

/// Announcements page displaying church announcements
class AnnouncementsPage extends StatelessWidget {
  const AnnouncementsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Column(
        children: [
          // Header
          const CustomHeader(pageTitle: 'Announcements'),

          // Main content
          Expanded(
            child: RefreshIndicator(
              onRefresh: () async {
                // TODO: Implement refresh logic
                await Future.delayed(const Duration(seconds: 1));
              },
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 8),

                    // Page title
                    Text(
                      'Latest Church Announcements',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.black,
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Announcements list
                    ..._buildAnnouncementsList(context),

                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildAnnouncementsList(BuildContext context) {
    // Mock data for announcements - can be empty to show empty state
    final announcements = [
      {
        'title': 'Sunday Service Schedule Change',
        'date': 'Today • 10:30 AM',
        'content':
            'This Sunday\'s service has been moved to 11:00 AM due to special preparations. Please arrive early for worship.',
        'author': 'Pastor Johnson',
        'isUrgent': true,
      },
      {
        'title': 'Youth Conference Registration Open',
        'date': 'Yesterday • 2:15 PM',
        'content':
            'Join us for the annual youth conference next month. Early bird registration ends this Friday.',
        'author': 'Youth Ministry',
        'isUrgent': false,
      },
      {
        'title': 'Prayer Meeting This Wednesday',
        'date': '2 days ago • 8:00 AM',
        'content':
            'We will have our weekly prayer meeting this Wednesday at 7:00 PM in the main sanctuary.',
        'author': 'Church Office',
        'isUrgent': false,
      },
      {
        'title': 'Community Outreach Program',
        'date': '3 days ago • 4:30 PM',
        'content':
            'Volunteers needed for our community outreach program this Saturday. Please sign up after service.',
        'author': 'Ministry Team',
        'isUrgent': false,
      },
    ];

    if (announcements.isEmpty) {
      return [_buildEmptyState()];
    }

    return announcements
        .map((announcement) => _buildAnnouncementCard(context, announcement))
        .toList();
  }

  Widget _buildAnnouncementCard(
    BuildContext context,
    Map<String, dynamic> announcement,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(
            color: Color(0x0A000000),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) =>
                    AnnouncementDetailPage(announcement: announcement),
              ),
            );
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header row with title and urgent badge
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        announcement['title'],
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppColors.black,
                          height: 1.3,
                        ),
                      ),
                    ),
                    if (announcement['isUrgent'] == true) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.warning.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Text(
                          'URGENT',
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                            color: AppColors.warning,
                            letterSpacing: 0.5,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),

                const SizedBox(height: 8),

                // Date and author
                Row(
                  children: [
                    Icon(Icons.access_time, size: 14, color: AppColors.grey),
                    const SizedBox(width: 4),
                    Text(
                      announcement['date'],
                      style: TextStyle(fontSize: 12, color: AppColors.grey),
                    ),
                    const SizedBox(width: 12),
                    Icon(Icons.person_outline, size: 14, color: AppColors.grey),
                    const SizedBox(width: 4),
                    Text(
                      announcement['author'],
                      style: TextStyle(fontSize: 12, color: AppColors.grey),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // Content preview
                Text(
                  announcement['content'],
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.onSurface,
                    height: 1.4,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: 12),

                // Read more indicator
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text(
                      'Tap to read more',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.secondary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 12,
                      color: AppColors.secondary,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          const SizedBox(height: 40),
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: AppColors.lightGrey.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              Icons.campaign_outlined,
              size: 40,
              color: AppColors.grey,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'No Announcements Yet',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.darkGrey,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Check back later for church updates and announcements.',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 14, color: AppColors.grey, height: 1.4),
          ),
          const SizedBox(height: 40),
        ],
      ),
    );
  }
}
