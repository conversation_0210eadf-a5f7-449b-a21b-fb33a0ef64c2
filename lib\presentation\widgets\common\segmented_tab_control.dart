import 'package:flutter/material.dart';
import '../../../core/theme/theme.dart';

/// Reusable segmented control widget for tabbed navigation
/// Used across the app for consistent tab styling
class SegmentedTabControl extends StatelessWidget {
  final List<String> tabs;
  final int selectedIndex;
  final ValueChanged<int> onTabChanged;
  final EdgeInsetsGeometry? padding;

  const SegmentedTabControl({
    required this.tabs,
    required this.selectedIndex,
    required this.onTabChanged,
    this.padding,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(
            color: Color(0x0A000000),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      padding: padding ?? const EdgeInsets.all(4),
      child: Row(
        children: tabs.asMap().entries.map((entry) {
          final index = entry.key;
          final text = entry.value;
          return Expanded(child: _buildSegmentButton(context, text, index));
        }).toList(),
      ),
    );
  }

  Widget _buildSegmentButton(BuildContext context, String text, int index) {
    final isSelected = selectedIndex == index;
    return GestureDetector(
      onTap: () => onTabChanged(index),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: isSelected
              ? AppColors.secondary.withValues(alpha: 0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: isSelected
              ? Border.all(color: AppColors.secondary, width: 1.5)
              : null,
        ),
        child: Center(
          child: Text(
            text,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              color: isSelected ? AppColors.secondary : AppColors.grey,
            ),
          ),
        ),
      ),
    );
  }
}
