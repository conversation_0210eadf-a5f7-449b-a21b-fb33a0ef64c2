import 'package:flutter/material.dart';
import 'package:flockin_v2_app/core/theme/theme.dart';
import 'top_navigation_bar.dart';

/// Custom header with app logo, date/title, and user profile card
/// Flexible header component for different pages
class CustomHeader extends StatelessWidget {
  final String? pageTitle;
  final String? userName;
  final String? userAvatar;
  final List<String>? userBadges;
  final VoidCallback? onEditPressed;

  const CustomHeader({
    super.key,
    this.pageTitle,
    this.userName,
    this.userAvatar,
    this.userBadges,
    this.onEditPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: AppColors.secondary, // Solid blue color
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(24),
          bottomRight: Radius.circular(24),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min, // Use minimum space needed
        children: [
          // Top navigation bar
          TopNavigationBar(pageTitle: pageTitle),

          // Profile card section inside header
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 8, 20, 20),
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // Profile Avatar
                  Container(
                    width: 56,
                    height: 56,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(color: AppColors.lightGrey, width: 2),
                    ),
                    child: ClipOval(
                      child: userAvatar != null
                          ? Image.network(
                              userAvatar!,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  _buildDefaultAvatar(),
                            )
                          : _buildDefaultAvatar(),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // Name and badges
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Name
                        Text(
                          userName ?? 'Angelica Martha Faozi',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: AppColors.black,
                                fontSize: 16,
                              ),
                        ),

                        const SizedBox(height: 6),

                        // Badges
                        Row(children: _buildUserBadges()),
                      ],
                    ),
                  ),

                  // Edit icon
                  GestureDetector(
                    onTap: onEditPressed,
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: AppColors.lightGrey.withValues(alpha: 0.5),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.edit,
                        size: 20,
                        color: AppColors.grey,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultAvatar() {
    return Container(
      color: AppColors.lightGrey,
      child: const Icon(Icons.person, size: 32, color: AppColors.grey),
    );
  }

  List<Widget> _buildUserBadges() {
    final badges = userBadges ?? ['Member', 'X Echo 1'];
    final badgeWidgets = <Widget>[];

    for (int i = 0; i < badges.length; i++) {
      badgeWidgets.add(_buildBadge(badges[i]));
      if (i < badges.length - 1) {
        badgeWidgets.add(const SizedBox(width: 8));
      }
    }

    return badgeWidgets;
  }

  Widget _buildBadge(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
      decoration: BoxDecoration(
        color: AppColors.secondary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Text(
        text,
        style: const TextStyle(
          color: AppColors.secondary,
          fontWeight: FontWeight.w500,
          fontSize: 11,
        ),
      ),
    );
  }
}
