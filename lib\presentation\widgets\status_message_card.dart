import 'package:flutter/material.dart';
import '../../core/theme/theme.dart';

/// Status message card with check icon and message
class StatusMessageCard extends StatefulWidget {
  const StatusMessageCard({super.key});

  @override
  State<StatusMessageCard> createState() => _StatusMessageCardState();
}

class _StatusMessageCardState extends State<StatusMessageCard> {
  bool _isVisible = true;

  @override
  Widget build(BuildContext context) {
    if (!_isVisible) {
      return const SizedBox.shrink();
    }

    return Container(
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(
            color: Color(0x0A000000),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(12),
      child: Row(
        children: [
          // Leading check icon
          Container(
            width: 44,
            height: 44,
            decoration: BoxDecoration(
              color: AppColors.success.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: const Icon(
              Icons.check_circle,
              color: AppColors.success,
              size: 30,
            ),
          ),

          const SizedBox(width: 12),

          // Message text
          Expanded(
            child: Text(
              'You attended service today. Blessings and peace be with you!',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.onSurface,
                height: 1.4,
                fontSize: 14,
              ),
            ),
          ),

          const SizedBox(width: 8),

          // Dismiss button
          GestureDetector(
            onTap: () {
              setState(() {
                _isVisible = false;
              });
            },
            child: Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: AppColors.grey.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.close, color: AppColors.grey, size: 14),
            ),
          ),
        ],
      ),
    );
  }
}
