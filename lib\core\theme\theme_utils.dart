import 'package:flutter/material.dart';
import 'app_colors.dart';

/// Utility class for theme-related helper methods
class ThemeUtils {
  ThemeUtils._();

  /// Returns whether the current theme is dark mode
  static bool isDarkMode(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark;
  }

  /// Returns the appropriate text color based on background
  static Color getTextColorForBackground(Color backgroundColor) {
    // Calculate luminance to determine if we need light or dark text
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? AppColors.black : AppColors.white;
  }

  /// Returns a color with the specified opacity
  static Color withOpacity(Color color, double opacity) {
    return color.withValues(alpha: opacity);
  }

  /// Returns a lighter shade of the given color
  static Color lighten(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslLight = hsl.withLightness(
      (hsl.lightness + amount).clamp(0.0, 1.0),
    );
    return hslLight.toColor();
  }

  /// Returns a darker shade of the given color
  static Color darken(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslDark = hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0));
    return hslDark.toColor();
  }

  /// Common spacing values
  static const double spacing4 = 4.0;
  static const double spacing8 = 8.0;
  static const double spacing12 = 12.0;
  static const double spacing16 = 16.0;
  static const double spacing20 = 20.0;
  static const double spacing24 = 24.0;
  static const double spacing32 = 32.0;
  static const double spacing48 = 48.0;

  /// Common border radius values
  static const double radiusSmall = 4.0;
  static const double radiusMedium = 8.0;
  static const double radiusLarge = 12.0;
  static const double radiusXLarge = 16.0;

  /// Common elevation values
  static const double elevationLow = 2.0;
  static const double elevationMedium = 4.0;
  static const double elevationHigh = 8.0;
  static const double elevationXHigh = 16.0;

  /// Get color based on status
  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'success':
      case 'completed':
      case 'active':
        return AppColors.success;
      case 'warning':
      case 'pending':
        return AppColors.warning;
      case 'error':
      case 'failed':
      case 'inactive':
        return AppColors.error;
      case 'info':
      case 'processing':
        return AppColors.secondary;
      default:
        return AppColors.grey;
    }
  }

  /// Get semantic colors for common UI states
  static WidgetStateProperty<Color?> getButtonColor({
    Color? defaultColor,
    Color? pressedColor,
    Color? disabledColor,
  }) {
    return WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
      if (states.contains(WidgetState.disabled)) {
        return disabledColor ?? AppColors.grey;
      }
      if (states.contains(WidgetState.pressed)) {
        return pressedColor ?? darken(defaultColor ?? AppColors.primary);
      }
      return defaultColor ?? AppColors.primary;
    });
  }
}
