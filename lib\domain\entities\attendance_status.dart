/// Attendance status enumeration
enum AttendanceStatus { present, late, absent }

/// Extension to provide display properties for AttendanceStatus
extension AttendanceStatusExtension on AttendanceStatus {
  String get displayName {
    switch (this) {
      case AttendanceStatus.present:
        return 'Present';
      case AttendanceStatus.late:
        return 'Late';
      case AttendanceStatus.absent:
        return 'Absent';
    }
  }

  String get description {
    switch (this) {
      case AttendanceStatus.present:
        return 'You\'re on time! Keep up the great work.';
      case AttendanceStatus.late:
        return 'You\'re running late. Please check in as soon as possible.';
      case AttendanceStatus.absent:
        return 'You\'re marked as absent. Please contact your supervisor.';
    }
  }
}
