import 'package:flutter/material.dart';
import '../../core/theme/theme.dart';
import '../widgets/custom_header.dart';
import '../widgets/status_message_card.dart';
import '../widgets/presence_section.dart';
import '../widgets/attendance_history_section.dart';

/// Home page with student attendance dashboard
class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Full-width header with integrated profile card
            const CustomHeader(),

            // Main content spacing
            const SizedBox(height: 24),
            // Main content with consistent padding
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 16),
                  // Status message card
                  const StatusMessageCard(),
                  const SizedBox(height: 24),
                  // Presence section
                  const PresenceSection(),
                  const SizedBox(height: 24),
                  // Attendance history
                  const AttendanceHistorySection(),
                  const SizedBox(height: 24),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
