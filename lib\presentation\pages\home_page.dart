import 'package:flutter/material.dart';
import '../../core/theme/theme.dart';
import '../widgets/custom_header.dart';
import '../widgets/profile_card.dart';
import '../widgets/status_message_card.dart';
import '../widgets/presence_section.dart';
import '../widgets/attendance_history_section.dart';

/// Home page with student attendance dashboard
class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Add top padding for status bar
            SizedBox(height: MediaQuery.of(context).padding.top),

            // Custom header with overlap for profile card
            Stack(
              children: [
                const CustomHeader(),
                // Profile card overlapping the header
                Positioned(
                  top: 100, // Adjusted for new header height and margin
                  left: 32,
                  right: 32,
                  child: const ProfileCard(),
                ),
              ],
            ),
            // Add spacing for the overlapped profile card
            const SizedBox(height: 60), // Adjusted spacing
            // Main content with consistent padding
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 16),
                  // Status message card
                  const StatusMessageCard(),
                  const SizedBox(height: 24),
                  // Presence section
                  const PresenceSection(),
                  const SizedBox(height: 24),
                  // Attendance history
                  const AttendanceHistorySection(),
                  const SizedBox(height: 24),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
