import 'package:flutter/material.dart';
import '../../../core/theme/theme.dart';

/// Example page demonstrating the custom theme
class ThemeExamplePage extends StatelessWidget {
  const ThemeExamplePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Flockin Theme Demo'),
        actions: [
          IconButton(
            icon: const Icon(Icons.palette),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Theme colors applied successfully!'),
                ),
              );
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Typography Examples
            Text(
              'Typography Examples',
              style: Theme.of(context).textTheme.headlineLarge,
            ),
            const SizedBox(height: 16),
            Text(
              'Headline Medium',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            Text('Title Large', style: Theme.of(context).textTheme.titleLarge),
            Text(
              'Body Large - This is a sample body text to show the typography in action.',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            Text(
              'Body Medium - Secondary text example.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 32),

            // Button Examples
            Text(
              'Button Examples',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 12,
              runSpacing: 12,
              children: [
                ElevatedButton(
                  onPressed: () {},
                  child: const Text('Elevated Button'),
                ),
                TextButton(onPressed: () {}, child: const Text('Text Button')),
                OutlinedButton(
                  onPressed: () {},
                  child: const Text('Outlined Button'),
                ),
              ],
            ),
            const SizedBox(height: 32),

            // Card Examples
            Text(
              'Card Examples',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Sample Card',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'This card demonstrates the rounded corners (12px radius) and light shadows as specified in the requirements.',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: () {},
                          child: const Text('Action'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 32),

            // Input Field Examples
            Text(
              'Input Field Examples',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 16),
            const TextField(
              decoration: InputDecoration(
                labelText: 'Normal Input',
                hintText: 'Enter some text',
              ),
            ),
            const SizedBox(height: 16),
            const TextField(
              decoration: InputDecoration(
                labelText: 'Error Input',
                hintText: 'This field has an error',
                errorText: 'This field is required',
              ),
            ),
            const SizedBox(height: 32),

            // Color Palette Display
            Text(
              'Color Palette',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 16),
            _buildColorPalette(context),
            const SizedBox(height: 32),

            // Interactive Elements
            Text(
              'Interactive Elements',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 16),
            _buildInteractiveElements(context),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Theme Dialog'),
              content: const Text(
                'This dialog demonstrates the theme styling.',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Close'),
                ),
              ],
            ),
          );
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildColorPalette(BuildContext context) {
    return Wrap(
      spacing: 12,
      runSpacing: 12,
      children: [
        _buildColorTile('Primary', AppColors.primary),
        _buildColorTile('Secondary', AppColors.secondary),
        _buildColorTile('Accent/Success', AppColors.accent),
        _buildColorTile('Warning/Orange', AppColors.warning),
        _buildColorTile('Background', AppColors.background),
      ],
    );
  }

  Widget _buildColorTile(String name, Color color) {
    return Container(
      width: 100,
      height: 80,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.lightGrey),
      ),
      child: Center(
        child: Text(
          name,
          style: TextStyle(
            color: color == AppColors.background
                ? AppColors.black
                : AppColors.white,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _buildInteractiveElements(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Switch(value: true, onChanged: (value) {}),
            const SizedBox(width: 16),
            const Text('Switch Example'),
          ],
        ),
        Row(
          children: [
            Checkbox(value: true, onChanged: (value) {}),
            const SizedBox(width: 16),
            const Text('Checkbox Example'),
          ],
        ),
        Row(
          children: [
            Radio<int>(value: 1, groupValue: 1, onChanged: (value) {}),
            const SizedBox(width: 16),
            const Text('Radio Example'),
          ],
        ),
        const SizedBox(height: 16),
        const LinearProgressIndicator(value: 0.6),
        const SizedBox(height: 8),
        const Text('Progress Indicator Example'),
      ],
    );
  }
}
