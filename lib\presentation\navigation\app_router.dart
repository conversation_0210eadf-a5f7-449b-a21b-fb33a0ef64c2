import 'package:go_router/go_router.dart';
import '../pages/home/<USER>';
import '../pages/attendance/attendances_page.dart';
import '../pages/profile/profile_page.dart';
import '../widgets/common/main_shell.dart';

/// App routing configuration using go_router with ShellRoute
class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: '/home',
    routes: [
      ShellRoute(
        builder: (context, state, child) => MainShell(child: child),
        routes: [
          GoRoute(
            path: '/home',
            name: 'home',
            builder: (context, state) => const HomePage(),
          ),
          GoRoute(
            path: '/attendances',
            name: 'attendances',
            builder: (context, state) => const AttendancesPage(),
          ),
          GoRoute(
            path: '/profile',
            name: 'profile',
            builder: (context, state) => const ProfilePage(),
          ),
        ],
      ),
    ],
  );
}
