import 'package:flutter/material.dart';
import '../../../core/theme/theme.dart';
import '../../widgets/common/custom_header.dart';
import '../../widgets/common/segmented_tab_control.dart';
import '../../widgets/media/sermons_tab_content.dart';
import '../../widgets/media/hymns_tab_content.dart';
import '../../widgets/media/devotionals_tab_content.dart';

/// Media page with tabbed interface for sermons, hymns, and devotionals
class MediaPage extends StatefulWidget {
  const MediaPage({super.key});

  @override
  State<MediaPage> createState() => _MediaPageState();
}

class _MediaPageState extends State<MediaPage> {
  int _selectedTabIndex = 0;
  final _tabs = ['Sermons', 'Hymns', 'Devotionals'];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Column(
        children: [
          // Header with media branding
          const CustomHeader(
            pageTitle: 'Media',
            userName: '<PERSON>',
            userBadges: ['Member', 'X Echo 1'],
          ),

          // Main content
          Expanded(
            child: Column(
              children: [
                // Tab spacing from header
                const SizedBox(height: 24),

                // Segmented tab control
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: SegmentedTabControl(
                    tabs: _tabs,
                    selectedIndex: _selectedTabIndex,
                    onTabChanged: (index) {
                      setState(() {
                        _selectedTabIndex = index;
                      });
                    },
                  ),
                ),

                const SizedBox(height: 8),

                // Tab content
                Expanded(child: _buildTabContent()),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabContent() {
    switch (_selectedTabIndex) {
      case 0:
        return const SermonsTabContent();
      case 1:
        return const HymnsTabContent();
      case 2:
        return const DevotionalsTabContent();
      default:
        return const SermonsTabContent();
    }
  }
}
