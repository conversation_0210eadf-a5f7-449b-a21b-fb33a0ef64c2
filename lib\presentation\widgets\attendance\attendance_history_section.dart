import 'package:flutter/material.dart';
import 'package:flockin_v2_app/core/theme/theme.dart';
import 'package:flockin_v2_app/presentation/widgets/common/segmented_tab_control.dart';

/// Attendance history section with list of attendance items
class AttendanceHistorySection extends StatefulWidget {
  const AttendanceHistorySection({super.key});

  @override
  State<AttendanceHistorySection> createState() =>
      _AttendanceHistorySectionState();
}

class _AttendanceHistorySectionState extends State<AttendanceHistorySection> {
  int _selectedIndex = 1; // Default to "This Month"

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Text(
          'Attendance History',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.black,
          ),
        ),

        const SizedBox(height: 16),

        // Segmented control for time period
        SegmentedTabControl(
          tabs: const ['This Week', 'This Month'],
          selectedIndex: _selectedIndex,
          onTabChanged: (index) {
            setState(() {
              _selectedIndex = index;
            });
          },
        ),

        const SizedBox(height: 20),

        // Placeholder for future attendance items
        Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: const [
              BoxShadow(
                color: Color(0x08000000),
                blurRadius: 8,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Center(
            child: Column(
              children: [
                Icon(
                  Icons.calendar_today_outlined,
                  size: 48,
                  color: AppColors.grey.withValues(alpha: 0.5),
                ),
                const SizedBox(height: 12),
                Text(
                  'No attendance records',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.grey,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Attendance history will appear here',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.grey.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
