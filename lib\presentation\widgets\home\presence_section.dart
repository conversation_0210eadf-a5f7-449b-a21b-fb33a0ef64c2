import 'package:flutter/material.dart';
import 'package:flockin_v2_app/core/theme/theme.dart';
import 'package:flockin_v2_app/presentation/widgets/common/segmented_tab_control.dart';

/// Presence section with segmented control and stat tiles
class PresenceSection extends StatefulWidget {
  const PresenceSection({super.key});

  @override
  State<PresenceSection> createState() => _PresenceSectionState();
}

class _PresenceSectionState extends State<PresenceSection> {
  int _selectedIndex = 0; // 0 for "This Week", 1 for "This Month"

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Text(
          'Presence Overview',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.black,
          ),
        ),

        const SizedBox(height: 16),

        // Segmented control for time period
        SegmentedTabControl(
          tabs: const ['This Week', 'This Month'],
          selectedIndex: _selectedIndex,
          onTabChanged: (index) {
            setState(() {
              _selectedIndex = index;
            });
          },
        ),

        const SizedBox(height: 20),

        // Stats grid
        Row(
          children: [
            Expanded(
              child: _buildStatTile('Arrive', '12', _getStatColor('arrive')),
            ),
            const SizedBox(width: 12),
            Expanded(child: _buildStatTile('Sick', '2', _getStatColor('sick'))),
          ],
        ),

        const SizedBox(height: 12),

        Row(
          children: [
            Expanded(
              child: _buildStatTile('Leave', '1', _getStatColor('leave')),
            ),
            const SizedBox(width: 12),
            Expanded(child: _buildStatTile('Skip', '0', _getStatColor('skip'))),
          ],
        ),
      ],
    );
  }

  Widget _buildStatTile(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(
            color: Color(0x08000000),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Big number
          Text(
            value,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              fontSize: 24,
              color: color,
            ),
          ),

          const SizedBox(height: 4),

          // Label
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontSize: 12,
              color: AppColors.grey,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatColor(String label) {
    switch (label.toLowerCase()) {
      case 'arrive':
        return AppColors.success;
      case 'sick':
        return AppColors.warning;
      case 'leave':
        return AppColors.secondary;
      case 'skip':
        return AppColors.error;
      default:
        return AppColors.grey;
    }
  }
}
