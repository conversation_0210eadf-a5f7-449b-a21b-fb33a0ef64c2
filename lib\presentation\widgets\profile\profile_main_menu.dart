import 'package:flutter/material.dart';
import '../../../core/theme/theme.dart';

/// Main menu section widget for the profile page
class ProfileMainMenuSection extends StatelessWidget {
  const ProfileMainMenuSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Text(
              'Main Menu',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.onSurface,
              ),
            ),
          ),
          const _ProfileMenuItem(
            icon: Icons.person_outline,
            title: 'Personal Information',
            isFirst: true,
          ),
          const _ProfileMenuItem(
            icon: Icons.settings_outlined,
            title: 'Settings',
          ),
          const _ProfileMenuItem(
            icon: Icons.help_outline,
            title: 'Help Center',
          ),
          const _ProfileMenuItem(
            icon: Icons.logout,
            title: 'Log Out',
            isLast: true,
            isDestructive: true,
          ),
        ],
      ),
    );
  }
}

/// Individual menu item widget
class _ProfileMenuItem extends StatelessWidget {
  const _ProfileMenuItem({
    required this.icon,
    required this.title,
    this.isFirst = false,
    this.isLast = false,
    this.isDestructive = false,
  });

  final IconData icon;
  final String title;
  final bool isFirst;
  final bool isLast;
  final bool isDestructive;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _handleTap(context),
        borderRadius: BorderRadius.vertical(
          top: isFirst ? const Radius.circular(12) : Radius.zero,
          bottom: isLast ? const Radius.circular(12) : Radius.zero,
        ),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          decoration: BoxDecoration(
            border: !isLast
                ? Border(
                    bottom: BorderSide(
                      color: AppColors.lightGrey.withValues(alpha: 0.5),
                      width: 1,
                    ),
                  )
                : null,
          ),
          child: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: isDestructive
                      ? AppColors.error.withValues(alpha: 0.1)
                      : AppColors.secondary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  icon,
                  color: isDestructive ? AppColors.error : AppColors.secondary,
                  size: 20,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: isDestructive
                        ? AppColors.error
                        : AppColors.onSurface,
                  ),
                ),
              ),
              Icon(Icons.chevron_right, color: AppColors.grey, size: 20),
            ],
          ),
        ),
      ),
    );
  }

  void _handleTap(BuildContext context) {
    // Handle menu item taps here
    switch (title) {
      case 'Personal Information':
        // Navigate to personal information page
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Personal Information tapped')),
        );
        break;
      case 'Settings':
        // Navigate to settings page
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Settings tapped')));
        break;
      case 'Help Center':
        // Navigate to help center
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Help Center tapped')));
        break;
      case 'Log Out':
        // Show confirmation dialog for logout
        _showLogoutDialog(context);
        break;
    }
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Log Out'),
          content: const Text('Are you sure you want to log out?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Handle logout logic here
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Logged out successfully')),
                );
              },
              style: TextButton.styleFrom(foregroundColor: AppColors.error),
              child: const Text('Log Out'),
            ),
          ],
        );
      },
    );
  }
}
