import 'package:flutter/material.dart';
import '../../core/theme/theme.dart';

/// Presence section with segmented control and stat tiles
class PresenceSection extends StatefulWidget {
  const PresenceSection({super.key});

  @override
  State<PresenceSection> createState() => _PresenceSectionState();
}

class _PresenceSectionState extends State<PresenceSection> {
  int _selectedIndex = 0; // 0 for "This Week", 1 for "This Month"

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Text(
          'Presence Overview',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.black,
          ),
        ),

        const SizedBox(height: 16),

        // Segmented control for time period
        Container(
          decoration: BoxDecoration(
            color: AppColors.lightGrey.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Expanded(child: _buildSegmentButton('This Week', 0)),
              Expanded(child: _buildSegmentButton('This Month', 1)),
            ],
          ),
        ),

        const SizedBox(height: 20),

        // Stats grid
        Row(
          children: [
            Expanded(
              child: _buildStatTile('Arrive', '12', _getStatColor('arrive')),
            ),
            const SizedBox(width: 12),
            Expanded(child: _buildStatTile('Sick', '2', _getStatColor('sick'))),
          ],
        ),

        const SizedBox(height: 12),

        Row(
          children: [
            Expanded(
              child: _buildStatTile('Leave', '1', _getStatColor('leave')),
            ),
            const SizedBox(width: 12),
            Expanded(child: _buildStatTile('Skip', '0', _getStatColor('skip'))),
          ],
        ),
      ],
    );
  }

  Widget _buildSegmentButton(String text, int index) {
    final isSelected = _selectedIndex == index;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedIndex = index;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.white : Colors.transparent,
          borderRadius: BorderRadius.circular(10),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: AppColors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Center(
          child: Text(
            text,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              color: isSelected ? AppColors.black : AppColors.grey,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatTile(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(
            color: Color(0x08000000),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Big number
          Text(
            value,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              fontSize: 24,
              color: color,
            ),
          ),

          const SizedBox(height: 4),

          // Label
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontSize: 12,
              color: AppColors.grey,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatColor(String label) {
    switch (label.toLowerCase()) {
      case 'arrive':
        return AppColors.success;
      case 'sick':
        return AppColors.warning;
      case 'leave':
        return AppColors.secondary;
      case 'skip':
        return AppColors.error;
      default:
        return AppColors.grey;
    }
  }
}
