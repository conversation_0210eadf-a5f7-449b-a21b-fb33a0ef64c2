import 'package:flutter/material.dart';
import 'package:flockin_v2_app/core/theme/theme.dart';

/// Simple announcements page for testing
class AnnouncementsPage extends StatelessWidget {
  const AnnouncementsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Announcements'),
        backgroundColor: AppColors.secondary,
        foregroundColor: AppColors.white,
      ),
      body: const Center(
        child: Text(
          'Announcements Page\nComing Soon!',
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
        ),
      ),
    );
  }
}
