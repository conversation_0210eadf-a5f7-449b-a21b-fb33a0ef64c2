# TopNavigationBar Component Usage Guide

## Overview

The `TopNavigationBar` is a reusable widget that provides consistent navigation across all screens in the Flutter app. It contains the app logo, current date, and notification bell icon with a blue background.

## Features

- **App Logo**: 't' text in a semi-transparent white container (left side)
- **Current Date**: Automatically formatted date display (center)
- **Notification Bell**: Interactive bell icon (right side)
- **Status Bar Safe Area**: Proper safe area handling for different devices
- **Consistent Styling**: Blue background (`AppColors.secondary`) with proper spacing

## Usage

### 1. Import the Component

```dart
import '../widgets/top_navigation_bar.dart';
```

### 2. Basic Implementation

```dart
@override
Widget build(BuildContext context) {
  return Scaffold(
    backgroundColor: AppColors.background,
    body: Column(
      children: [
        // Add the reusable top navigation bar
        const TopNavigationBar(),

        // Your page content goes here
        Expanded(
          child: YourPageContent(),
        ),
      ],
    ),
  );
}
```

### 3. DO NOT use with AppBar

When using `TopNavigationBar`, do **NOT** include an `AppBar` in your Scaffold:

```dart
// ❌ WRONG - Don't use both
Scaffold(
  appBar: AppBar(...), // Remove this
  body: Column(
    children: [
      const TopNavigationBar(), // And use this
      ...
    ],
  ),
)

// ✅ CORRECT - Use only TopNavigationBar
Scaffold(
  body: Column(
    children: [
      const TopNavigationBar(),
      ...
    ],
  ),
)
```

## Examples

### Home Screen (with Profile Card)

```dart
// CustomHeader already implements TopNavigationBar
const CustomHeader() // This includes TopNavigationBar + Profile Card
```

### Attendance Screen

```dart
Column(
  children: [
    const TopNavigationBar(),
    Expanded(
      child: AttendanceContent(),
    ),
  ],
)
```

### Profile Screen

```dart
Column(
  children: [
    const TopNavigationBar(),
    Expanded(
      child: ProfileContent(),
    ),
  ],
)
```

## Component Structure

```
TopNavigationBar
├── Blue background (AppColors.secondary)
├── SafeArea for status bar
├── Horizontal padding (20px)
├── Vertical padding (12px)
└── Row with:
    ├── App Logo (40x40px, rounded, semi-transparent white)
    ├── Date Text (center, white, medium weight)
    └── Bell Icon (40x40px, rounded, semi-transparent white)
```

## Date Format

The component automatically displays the current date in the format:

- "Wednesday, 10 September 2025"
- Updates automatically based on system date

## Styling Consistency

- Uses `AppColors.secondary` for background
- Uses `AppColors.white` for text and icons
- Semi-transparent white containers for logo and bell (20% opacity)
- 12px border radius for rounded corners
- Consistent spacing and typography

## Benefits

1. **Code Reusability**: No duplication across screens
2. **Consistent UX**: Same navigation experience everywhere
3. **Easy Maintenance**: Update once, applies everywhere
4. **Theme Compliance**: Uses app's color system
5. **Responsive Design**: Works on all screen sizes
