import 'package:flutter/material.dart';
import '../../core/theme/theme.dart';
import 'custom_badge.dart';

/// Profile card widget with avatar, name, badges, and edit icon
class ProfileCard extends StatelessWidget {
  const ProfileCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shadowColor: AppColors.black.withValues(alpha: 0.1),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Profile Avatar
            Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: AppColors.lightGrey, width: 2),
              ),
              child: ClipOval(
                child: Container(
                  color: AppColors.lightGrey,
                  child: const Icon(
                    Icons.person,
                    size: 32,
                    color: AppColors.grey,
                  ),
                ),
              ),
            ),

            const SizedBox(width: 16),

            // Name and badges
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Name
                  Text(
                    '<PERSON>',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.black,
                    ),
                  ),

                  const SizedBox(height: 8),

                  // Badges
                  Row(
                    children: [
                      const CustomBadge(text: 'Student'),
                      const SizedBox(width: 8),
                      const CustomBadge(text: 'X Echo 1'),
                    ],
                  ),
                ],
              ),
            ),

            // Edit icon
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppColors.lightGrey.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(Icons.edit, size: 20, color: AppColors.grey),
            ),
          ],
        ),
      ),
    );
  }
}
