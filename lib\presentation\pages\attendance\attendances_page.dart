import 'package:flutter/material.dart';
import 'package:flockin_v2_app/core/theme/theme.dart';
import 'package:flockin_v2_app/presentation/widgets/common/top_navigation_bar.dart';

/// Attendances page - placeholder for detailed attendance view
class AttendancesPage extends StatelessWidget {
  const AttendancesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Column(
        children: [
          // Reusable top navigation bar
          const TopNavigationBar(),

          // Main content
          Expanded(
            child: const Center(
              child: Padding(
                padding: EdgeInsets.all(24.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.calendar_today, size: 64, color: AppColors.grey),
                    SizedBox(height: 16),
                    Text(
                      'Detailed Attendances',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: AppColors.onSurface,
                      ),
                    ),
                    Sized<PERSON><PERSON>(height: 8),
                    Text(
                      'This page will show detailed attendance records, calendar view, and filtering options.',
                      textAlign: TextAlign.center,
                      style: TextStyle(fontSize: 16, color: AppColors.grey),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
