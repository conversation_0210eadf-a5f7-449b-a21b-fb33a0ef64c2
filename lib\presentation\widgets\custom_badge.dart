import 'package:flutter/material.dart';
import '../../core/theme/theme.dart';

/// Custom badge widget for displaying status or category labels
class CustomBadge extends StatelessWidget {
  const CustomBadge({
    required this.text,
    this.backgroundColor,
    this.textColor,
    super.key,
  });

  final String text;
  final Color? backgroundColor;
  final Color? textColor;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.secondary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: textColor ?? AppColors.secondary,
          fontWeight: FontWeight.w500,
          fontSize: 12,
        ),
      ),
    );
  }
}
