import 'package:flutter/material.dart';
import 'package:flockin_v2_app/core/theme/theme.dart';
import 'package:flockin_v2_app/domain/entities/attendance_status.dart';

/// Status message card with check icon and message
/// Now supports three attendance states: ABSENT, PRESENT, LATE
class StatusMessageCard extends StatelessWidget {
  const StatusMessageCard({super.key, this.status = AttendanceStatus.present});

  final AttendanceStatus status;

  @override
  Widget build(BuildContext context) {
    final statusData = _getStatusData(status);

    return Container(
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(
            color: Color(0x0A000000),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(12),
      child: Row(
        children: [
          // Leading status icon
          Container(
            width: 44,
            height: 44,
            decoration: BoxDecoration(
              color: statusData.color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(statusData.icon, color: statusData.color, size: 30),
          ),

          const SizedBox(width: 12),

          // Message text
          Expanded(
            child: Text(
              statusData.message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.onSurface,
                height: 1.4,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  _StatusData _getStatusData(AttendanceStatus status) {
    switch (status) {
      case AttendanceStatus.present:
        return _StatusData(
          color: AppColors.success,
          icon: Icons.check_circle,
          message: 'You have arrived on time',
        );
      case AttendanceStatus.late:
        return _StatusData(
          color: AppColors.warning,
          icon: Icons.access_time,
          message: 'You arrived late',
        );
      case AttendanceStatus.absent:
        return _StatusData(
          color: AppColors.error,
          icon: Icons.cancel_outlined,
          message: 'You are marked as absent',
        );
    }
  }
}

/// Internal class for status data
class _StatusData {
  final Color color;
  final IconData icon;
  final String message;

  _StatusData({required this.color, required this.icon, required this.message});
}
